# 排列5彩票投注系统

## 项目简介

这是一个基于Web的排列5彩票投注系统，支持用户进行投注、查看开奖结果、管理投注记录等功能。

## 功能特性

### 🎯 投注功能
- **多种投注方式**：支持二字定、三字定、四字定投注
- **灵活选号**：万、仟、佰、拾位可独立选择或使用通配符(x)
- **积分系统**：基于积分进行投注，支持多注投注
- **实时验证**：投注前自动验证积分余额和选号有效性

### 📊 开奖功能
- **实时采集**：点击按钮即可采集最新开奖结果
- **自动结算**：开奖后自动结算所有待开奖投注
- **历史记录**：保存开奖历史，方便查询

### 💰 奖金系统
- **二字定**：赔率 1:95
- **三字定**：赔率 1:950  
- **四字定**：赔率 1:9500
- **自动计算**：中奖后自动计算并发放奖金

### 📝 记录管理
- **投注记录**：显示最近20条投注记录
- **状态跟踪**：待开奖、中奖、未中奖状态清晰显示
- **撤单功能**：待开奖投注支持撤单退款
- **数据持久化**：使用localStorage保存用户数据

## 技术架构

### 前端技术
- **HTML5**：页面结构
- **CSS3**：样式设计，响应式布局
- **JavaScript ES6+**：核心逻辑实现
- **LocalStorage**：本地数据存储

### 文件结构
```
pl5/
├── index.html          # 主页面
├── script.js           # 核心JavaScript逻辑
├── style.css           # 样式文件
└── README.md           # 项目说明文档
```

## 核心功能模块

### 1. 用户管理模块
- 用户积分管理
- 投注统计
- 数据持久化

### 2. 投注模块
- 选号界面
- 投注类型选择
- 积分扣除
- 投注记录生成

### 3. 开奖模块
- API数据获取
- 开奖结果解析
- 历史数据管理

### 4. 结算模块
- 中奖号码匹配
- 奖金计算
- 积分发放
- 状态更新

## 使用说明

### 投注流程
1. 在万、仟、佰、拾位输入框中选择号码（0-9）或使用通配符(x)
2. 选择投注类型（二字定/三字定/四字定）
3. 设置投注积分和注数
4. 点击"确认投注"按钮
5. 系统自动扣除积分并生成投注记录

### 开奖流程
1. 点击"采集开奖结果"按钮
2. 系统自动获取最新开奖数据
3. 更新当前期号和中奖号码
4. 自动结算所有待开奖投注
5. 中奖用户自动获得奖金

### 撤单操作
1. 在投注记录中找到待开奖的投注
2. 点击"撤单"按钮
3. 确认撤单后自动退还投注积分

## 投注规则

### 投注类型说明
- **二字定**：选择2个位置的号码，其他位置用x表示
- **三字定**：选择3个位置的号码，其他位置用x表示
- **四字定**：选择4个位置的号码

### 中奖规则
- 所选位置的号码与开奖号码完全一致即为中奖
- 通配符(x)位置不参与中奖判断
- 中奖奖金 = 投注积分 × 对应赔率

## 数据存储

系统使用浏览器的localStorage进行数据持久化：
- `pl5_user`：用户信息和积分数据
- `pl5_history`：开奖历史记录
- `pl5_records`：投注记录数据
- `pl5_current`：当前开奖信息

## 开发说明

### 本地运行
1. 下载项目文件到本地
2. 使用Web服务器打开index.html
3. 或直接在浏览器中打开index.html文件

### 调试功能
- 浏览器控制台包含详细的调试信息
- 投注和结算过程都有相应的日志输出
- 支持手动清除localStorage数据重置系统

## 注意事项

1. **数据安全**：所有数据存储在本地浏览器中，清除浏览器数据会丢失所有记录
2. **网络依赖**：开奖结果采集需要网络连接
3. **浏览器兼容**：建议使用现代浏览器（Chrome、Firefox、Safari等）
4. **积分管理**：请合理管理积分，避免过度投注

## 更新日志

### v1.0.0
- 基础投注功能实现
- 开奖结果采集
- 自动结算系统
- 投注记录管理
- 撤单功能
- 数据持久化

---

**开发者**：AI Assistant  
**最后更新**：2024年  
**许可证**：MIT License