// 全局变量
let currentUser = {
    points: 10000,
    totalBets: 0,
    totalWinnings: 0
};

let drawHistory = [];
let betRecords = [];
let currentIssue = null;
let currentWinningNumbers = null;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    bindEvents();
    loadUserData();
    updateDisplay();
});

// 初始化应用
function initializeApp() {
    // 从localStorage加载数据
    const savedUser = localStorage.getItem('pl5_user');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
    }
    
    const savedHistory = localStorage.getItem('pl5_history');
    if (savedHistory) {
        drawHistory = JSON.parse(savedHistory);
    }
    
    const savedRecords = localStorage.getItem('pl5_records');
    if (savedRecords) {
        betRecords = JSON.parse(savedRecords);
    }
    
    // 加载当前开奖结果
    const savedCurrentDraw = localStorage.getItem('pl5_current_draw');
    if (savedCurrentDraw) {
        const currentDraw = JSON.parse(savedCurrentDraw);
        currentIssue = currentDraw.issue;
        currentWinningNumbers = currentDraw.numbers;
        
        // 恢复开奖结果显示
        if (currentIssue && currentWinningNumbers) {
            document.getElementById('current-issue').textContent = currentIssue;
            document.getElementById('draw-time').textContent = currentDraw.time || '-';
            
            for (let i = 0; i < 5 && i < currentWinningNumbers.length; i++) {
                document.getElementById(`num${i + 1}`).textContent = currentWinningNumbers[i];
            }
        }
    }
}

// 绑定事件
function bindEvents() {
    // 开奖相关
    document.getElementById('fetch-result').addEventListener('click', fetchLotteryResult);
    
    // 选号相关
    ['pos1', 'pos2', 'pos3', 'pos4'].forEach(id => {
        document.getElementById(id).addEventListener('input', validateInput);
        document.getElementById(id).addEventListener('input', updateBetInfo);
    });
    
    document.getElementById('bet-points').addEventListener('input', updateTotalBet);
    document.getElementById('place-bet').addEventListener('click', placeBet);
    document.getElementById('clear-selection').addEventListener('click', clearSelection);
    
    // 用户中心相关
    document.getElementById('recharge-btn').addEventListener('click', rechargePoints);
    document.getElementById('reset-points').addEventListener('click', resetPoints);
}

// 采集开奖结果
async function fetchLotteryResult() {
    try {
        showMessage('正在采集开奖结果...', 'info');
        
        const response = await fetch('https://api.api68.com/QuanGuoCai/getLotteryInfo.do?lotCode=10044');
        const data = await response.json();
        
        if (data.errorCode === 0 && data.result && data.result.data) {
            const lotteryData = data.result.data;
            const issue = lotteryData.preDrawIssue;
            const numbers = lotteryData.preDrawCode.split(',');
            
            // 检查是否已经是最新期号
            if (currentIssue === issue) {
                showMessage('当前已经是最新开奖结果，无需重复采集', 'warning');
                return;
            }
            
            if (numbers.length >= 5) {
                currentIssue = String(issue); // 确保期号为字符串类型
                currentWinningNumbers = numbers.slice(0, 5);
                console.log('设置当前期号为:', currentIssue, '类型:', typeof currentIssue);
                
                // 更新显示
                document.getElementById('current-issue').textContent = issue;
                document.getElementById('draw-time').textContent = lotteryData.preDrawTime;
                
                for (let i = 0; i < 5; i++) {
                    document.getElementById(`num${i + 1}`).textContent = numbers[i];
                }
                
                // 添加到历史记录
                addToHistory(issue, numbers.slice(0, 5), lotteryData.preDrawTime);
                
                // 触发结算
                console.log('=== 开始结算流程 ===');
                console.log('当前期号:', currentIssue);
                console.log('中奖号码:', currentWinningNumbers);
                console.log('总投注记录数:', betRecords.length);
                const pendingBets = betRecords.filter(r => r.status === 'pending');
                console.log('待结算投注记录数:', pendingBets.length);
                if (pendingBets.length > 0) {
                    console.log('待结算记录详情:', pendingBets.map(r => ({id: r.id, issue: r.issue, content: r.betContent})));
                }
                settleAllBets();
                console.log('=== 结算流程结束 ===');
                
                showMessage('开奖结果采集成功！', 'success');
            } else {
                showMessage('开奖数据格式错误', 'error');
            }
        } else {
            showMessage('采集开奖结果失败', 'error');
        }
    } catch (error) {
        console.error('采集开奖结果错误:', error);
        showMessage('网络错误，采集失败', 'error');
    }
}

// 生成下期期号
function getNextIssue() {
    if (!currentIssue) {
        return '待开奖';
    }
    
    // 当前期号加1
    const currentNum = parseInt(currentIssue);
    const nextNum = currentNum + 1;
    return nextNum.toString();
}



// 添加到历史记录
function addToHistory(issue, numbers, time) {
    const historyItem = {
        issue: issue,
        numbers: numbers,
        time: time
    };
    
    drawHistory.unshift(historyItem);
    
    // 只保留最近两期
    if (drawHistory.length > 2) {
        drawHistory = drawHistory.slice(0, 2);
    }
    
    updateHistoryDisplay();
    saveData();
}

// 更新历史记录显示
function updateHistoryDisplay() {
    const historyList = document.getElementById('history-list');
    historyList.innerHTML = '';
    
    drawHistory.forEach(item => {
        const historyDiv = document.createElement('div');
        historyDiv.className = 'history-item fade-in';
        
        const numbersHtml = item.numbers.map(num => 
            `<span class="history-number">${num}</span>`
        ).join('');
        
        historyDiv.innerHTML = `
            <div>
                <strong>期号：${item.issue}</strong><br>
                <small>${item.time}</small>
            </div>
            <div class="history-numbers">${numbersHtml}</div>
        `;
        
        historyList.appendChild(historyDiv);
    });
}

// 验证输入
function validateInput(event) {
    const input = event.target;
    let value = input.value;
    
    // 只允许数字0-9
    value = value.replace(/[^0-9]/g, '');
    
    // 去除重复数字
    const uniqueChars = [...new Set(value)];
    value = uniqueChars.join('');
    
    // 限制最多10个字符
    if (value.length > 10) {
        value = value.slice(0, 10);
    }
    
    input.value = value;
}

// 更新投注信息
function updateBetInfo() {
    const positions = ['pos1', 'pos2', 'pos3', 'pos4'];
    const filledPositions = [];
    
    positions.forEach((id, index) => {
        const value = document.getElementById(id).value.trim();
        if (value.length > 0) {
            filledPositions.push({
                position: index,
                numbers: value.split(''),
                count: value.length
            });
        }
    });
    
    let betType = '';
    let betCount = 0;
    let odds = 0;
    
    if (filledPositions.length === 0) {
        betType = '请选择号码';
    } else if (filledPositions.length === 1) {
        betType = '至少选择两个位置';
    } else if (filledPositions.length === 2) {
        betType = '二字定';
        betCount = filledPositions[0].count * filledPositions[1].count;
        odds = 95;
    } else if (filledPositions.length === 3) {
        betType = '三字定';
        betCount = filledPositions.reduce((acc, pos) => acc * pos.count, 1);
        odds = 950;
    } else if (filledPositions.length === 4) {
        betType = '四字定';
        betCount = filledPositions.reduce((acc, pos) => acc * pos.count, 1);
        odds = 9500;
    }
    
    document.getElementById('bet-type-display').textContent = betType;
    document.getElementById('bet-count-display').textContent = betCount;
    document.getElementById('odds-display').textContent = odds > 0 ? `1:${odds}` : '-';
    
    updateTotalBet();
}

// 更新总投注
function updateTotalBet() {
    const betPoints = parseInt(document.getElementById('bet-points').value) || 0;
    const betCount = parseInt(document.getElementById('bet-count-display').textContent) || 0;
    const totalBet = betPoints * betCount;
    
    document.getElementById('total-bet').textContent = totalBet;
}

// 下注
function placeBet() {
    const betPoints = parseInt(document.getElementById('bet-points').value) || 0;
    const betCount = parseInt(document.getElementById('bet-count-display').textContent) || 0;
    const betType = document.getElementById('bet-type-display').textContent;
    const totalBet = betPoints * betCount;
    
    // 验证
    if (betType === '请选择号码' || betType === '至少选择两个位置') {
        showMessage('请正确选择号码', 'warning');
        return;
    }
    
    if (betPoints <= 0) {
        showMessage('请输入有效的下注积分', 'warning');
        return;
    }
    
    if (totalBet > currentUser.points) {
        showMessage('积分不足', 'error');
        return;
    }
    
    // 获取选号内容
    const positions = ['pos1', 'pos2', 'pos3', 'pos4'];
    const betContent = positions.map(id => {
        const value = document.getElementById(id).value.trim();
        return value || 'x';
    }).join('');
    
    // 创建下注记录 - 投注的是下期开奖
    const betRecord = {
        id: Date.now(),
        issue: getNextIssue(), // 投注下期期号
        betContent: betContent,
        betType: betType,
        betPoints: betPoints,
        betCount: betCount,
        totalBet: totalBet,
        status: 'pending',
        winnings: 0,
        timestamp: new Date().toLocaleString('zh-CN')
    };
    
    console.log('创建投注记录，期号:', betRecord.issue, '当前期号:', currentIssue);
    
    // 扣除积分
    currentUser.points -= totalBet;
    currentUser.totalBets += totalBet;
    
    // 添加记录
    betRecords.unshift(betRecord);
    
    // 更新显示
    updateDisplay();
    clearSelection();
    
    showMessage(`下注成功！共${betCount}注，消耗${totalBet}积分`, 'success');
    
    saveData();
}

// 清空选择
function clearSelection() {
    ['pos1', 'pos2', 'pos3', 'pos4'].forEach(id => {
        document.getElementById(id).value = '';
    });
    document.getElementById('bet-points').value = '';
    updateBetInfo();
}

// 充值积分
function rechargePoints() {
    const amount = parseInt(document.getElementById('recharge-amount').value) || 0;
    
    if (amount <= 0) {
        showMessage('请输入有效的充值金额', 'warning');
        return;
    }
    
    currentUser.points += amount;
    document.getElementById('recharge-amount').value = '';
    
    updateDisplay();
    showMessage(`充值成功！获得${amount}积分`, 'success');
    
    saveData();
}

// 重置积分
function resetPoints() {
    if (confirm('确定要重置所有数据吗？此操作不可恢复！')) {
        currentUser = {
            points: 10000,
            totalBets: 0,
            totalWinnings: 0
        };
        betRecords = [];
        drawHistory = [];
        
        updateDisplay();
        showMessage('数据重置成功！', 'info');
        
        saveData();
    }
}

// 撤单功能
function cancelBet(betId) {
    const recordIndex = betRecords.findIndex(record => record.id === betId);
    
    if (recordIndex === -1) {
        showMessage('投注记录不存在', 'error');
        return;
    }
    
    const record = betRecords[recordIndex];
    
    if (record.status !== 'pending') {
        showMessage('只能撤销待开奖的投注', 'warning');
        return;
    }
    
    if (confirm(`确定要撤销这笔投注吗？\n期号：${record.issue}\n投注内容：${record.betContent}\n投注积分：${record.totalBet}`)) {
        // 退还积分
        currentUser.points += record.totalBet;
        currentUser.totalBets -= record.totalBet;
        
        // 删除投注记录
        betRecords.splice(recordIndex, 1);
        
        // 更新显示
        updateDisplay();
        
        showMessage(`撤单成功！已退还${record.totalBet}积分`, 'success');
        
        saveData();
    }
}

// 结算所有投注
function settleAllBets() {
    if (!currentWinningNumbers || currentWinningNumbers.length < 4) {
        console.log('结算失败：没有中奖号码或号码不足4位');
        return;
    }
    
    const winningNumbers = currentWinningNumbers.slice(0, 4); // 只取前4位
    let totalWinnings = 0;
    let settledCount = 0;
    
    console.log('结算函数执行，当前期号:', currentIssue);
    console.log('中奖号码:', winningNumbers);
    
    betRecords.forEach(record => {
        console.log('检查投注记录:', record.issue, record.status, '当前期号:', currentIssue);
        console.log('期号比较:', record.issue, '===', currentIssue, '结果:', record.issue === currentIssue);
        console.log('期号类型:', typeof record.issue, typeof currentIssue);
        // 结算逻辑：投注时记录的是下期期号，当该期开奖时进行结算
        // 投注记录的期号应该等于当前开奖期号（投注的下期现在开奖了）
        // 确保期号类型一致进行比较
        if (record.status === 'pending' && String(record.issue) === String(currentIssue)) {
            console.log('找到匹配的投注记录进行结算:', record.id, record.betContent);
            const isWin = checkWin(record.betContent, winningNumbers, record.betType);
            
            if (isWin) {
                let odds = 0;
                if (record.betType === '二字定') odds = 95;
                else if (record.betType === '三字定') odds = 950;
                else if (record.betType === '四字定') odds = 9500;
                
                record.winnings = record.betPoints * odds;
                record.status = 'win';
                currentUser.points += record.winnings;
                totalWinnings += record.winnings;
            } else {
                record.status = 'lose';
            }
            settledCount++;
        }
    });
    
    if (settledCount > 0) {
        if (totalWinnings > 0) {
            currentUser.totalWinnings += totalWinnings;
            showMessage(`开奖结算完成！共结算${settledCount}注投注，中奖获得${totalWinnings}积分`, 'success');
        } else {
            showMessage(`开奖结算完成！共结算${settledCount}注投注，很遗憾未中奖`, 'info');
        }
    } else {
        console.log('没有找到需要结算的投注记录');
        const pendingRecords = betRecords.filter(r => r.status === 'pending');
        console.log('当前待开奖记录数量:', pendingRecords.length);
        if (pendingRecords.length > 0) {
            console.log('待开奖记录期号:', pendingRecords.map(r => r.issue));
            showMessage('没有找到匹配当前期号的投注记录，请检查期号是否正确', 'warning');
        }
    }
    
    updateDisplay();
    saveData();
}

// 检查是否中奖
function checkWin(betContent, winningNumbers, betType) {
    const betPositions = [];
    
    for (let i = 0; i < 4; i++) {
        if (betContent[i] !== 'x') {
            betPositions.push({
                position: i,
                numbers: betContent[i].split('')
            });
        }
    }
    
    // 检查每个位置是否匹配
    for (let pos of betPositions) {
        if (!pos.numbers.includes(winningNumbers[pos.position])) {
            return false;
        }
    }
    
    return true;
}

// 更新显示
function updateDisplay() {
    // 更新用户中心
    document.getElementById('current-points').textContent = currentUser.points;
    document.getElementById('total-bets').textContent = currentUser.totalBets;
    document.getElementById('total-winnings').textContent = currentUser.totalWinnings;
    
    // 更新下期期号显示
    document.getElementById('next-issue').textContent = getNextIssue();
    
    // 更新下注记录
    updateBetRecords();
}

// 更新下注记录显示
function updateBetRecords() {
    const recordsList = document.getElementById('records-list');
    recordsList.innerHTML = '';
    
    betRecords.slice(0, 20).forEach(record => { // 只显示最近20条
        const recordDiv = document.createElement('div');
        recordDiv.className = 'record-item fade-in';
        
        let statusText = '';
        let statusClass = '';
        
        if (record.status === 'pending') {
            statusText = '待开奖';
            statusClass = 'status-pending';
        } else if (record.status === 'win') {
            statusText = '中奖';
            statusClass = 'status-win';
        } else {
            statusText = '未中奖';
            statusClass = 'status-lose';
        }
        
        const actionButton = record.status === 'pending' ? 
            `<button class="btn btn-cancel" onclick="cancelBet(${record.id})">撤单</button>` : 
            '<span>-</span>';
        
        // 查找对应期号的开奖号码
        let winningNumbersDisplay = '';
        if (record.status !== 'pending') {
            // 从历史记录中查找对应期号的开奖号码
            const historyItem = drawHistory.find(item => item.issue === record.issue);
            if (historyItem && historyItem.numbers) {
                winningNumbersDisplay = `<div class="winning-numbers-small">${historyItem.numbers.join(' ')}</div>`;
            } else if (currentIssue === record.issue && currentWinningNumbers) {
                // 如果是当前期号，使用当前开奖号码
                winningNumbersDisplay = `<div class="winning-numbers-small">${currentWinningNumbers.join(' ')}</div>`;
            }
        }
        
        recordDiv.innerHTML = `
            <div>
                ${record.issue}
                ${winningNumbersDisplay}
            </div>
            <div>
                <div class="bet-content">${record.betContent}</div>
                <small>${record.betType} (${record.betCount}注)</small>
            </div>
            <div>${record.totalBet}</div>
            <div class="${statusClass}">${statusText}</div>
            <div>${record.winnings}</div>
            <div>${actionButton}</div>
        `;
        
        recordsList.appendChild(recordDiv);
    });
}

// 显示消息
function showMessage(text, type = 'info') {
    // 移除现有消息
    const existingMessage = document.querySelector('.message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    const message = document.createElement('div');
    message.className = `message ${type}`;
    message.textContent = text;
    
    document.body.appendChild(message);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (message.parentNode) {
            message.remove();
        }
    }, 3000);
}

// 保存数据
function saveData() {
    localStorage.setItem('pl5_user', JSON.stringify(currentUser));
    localStorage.setItem('pl5_history', JSON.stringify(drawHistory));
    localStorage.setItem('pl5_records', JSON.stringify(betRecords));
    
    // 保存当前开奖结果
    if (currentIssue && currentWinningNumbers) {
        const currentDraw = {
            issue: currentIssue,
            numbers: currentWinningNumbers,
            time: document.getElementById('draw-time').textContent
        };
        localStorage.setItem('pl5_current_draw', JSON.stringify(currentDraw));
    }
}

// 加载用户数据
function loadUserData() {
    updateDisplay();
    updateHistoryDisplay();
}

// 页面可见性变化时保存数据
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        saveData();
    }
});

// 页面卸载时保存数据
window.addEventListener('beforeunload', function() {
    saveData();
});