* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    color: white;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 10px;
}

section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

section h2 {
    color: #4a5568;
    font-size: 1.8em;
    margin-bottom: 20px;
    border-bottom: 3px solid #667eea;
    padding-bottom: 10px;
}

section h3 {
    color: #2d3748;
    font-size: 1.3em;
    margin: 20px 0 15px 0;
}

/* 开奖结果模块 */
.current-draw {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 12px;
    padding: 20px;
    color: white;
    margin-bottom: 20px;
}

.draw-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.number-display {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 10px;
}

.position {
    background: rgba(255,255,255,0.2);
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.9em;
    min-width: 60px;
    text-align: center;
}

.numbers {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

.number {
    background: white;
    color: #333;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.draw-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-primary {
    background: #4299e1;
    color: white;
}

.btn-primary:hover {
    background: #3182ce;
    transform: translateY(-2px);
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover {
    background: #38a169;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #a0aec0;
    color: white;
}

.btn-secondary:hover {
    background: #718096;
    transform: translateY(-2px);
}

.btn-warning {
    background: #ed8936;
    color: white;
}

.btn-warning:hover {
    background: #dd6b20;
    transform: translateY(-2px);
}

.btn-cancel {
    background: #f56565;
    color: white;
    padding: 6px 12px;
    font-size: 0.875em;
}

.btn-cancel:hover {
    background: #e53e3e;
    transform: translateY(-2px);
}

/* 开奖历史 */
.draw-history {
    background: #f7fafc;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 10px;
}

.history-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.history-numbers {
    display: flex;
    gap: 5px;
}

.history-number {
    background: #667eea;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9em;
    font-weight: bold;
}

.winning-numbers-small {
    font-size: 0.8em;
    color: #667eea;
    font-weight: bold;
    margin-top: 3px;
    font-family: monospace;
}

/* 中奖规则 */
.winning-rules {
    background: #edf2f7;
    border-radius: 8px;
    padding: 15px;
}

.rules-content ul {
    list-style: none;
    padding-left: 0;
}

.rules-content li {
    background: white;
    padding: 10px 15px;
    margin-bottom: 8px;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

/* 选号区域 */
.number-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.position-input {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.position-input label {
    font-weight: bold;
    color: #4a5568;
}

.position-input input {
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1em;
    transition: border-color 0.3s ease;
}

.position-input input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.bet-info {
    display: flex;
    justify-content: space-around;
    background: #f7fafc;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.bet-info span {
    font-weight: bold;
}

.bet-amount {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.bet-amount input {
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1em;
    width: 150px;
}

.bet-amount input:focus {
    outline: none;
    border-color: #667eea;
}

.bet-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* 用户中心 */
.user-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.stat-item label {
    display: block;
    font-size: 0.9em;
    margin-bottom: 8px;
    opacity: 0.9;
}

.stat-item span {
    font-size: 1.8em;
    font-weight: bold;
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.recharge {
    display: flex;
    gap: 10px;
    align-items: center;
}

.recharge input {
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1em;
    width: 150px;
}

.recharge input:focus {
    outline: none;
    border-color: #667eea;
}

/* 下注记录 */
.records-header {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1fr 1fr;
    gap: 15px;
    background: #4a5568;
    color: white;
    padding: 15px;
    border-radius: 8px;
    font-weight: bold;
    margin-bottom: 10px;
}

.record-item {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1fr 1fr;
    gap: 15px;
    padding: 15px;
    background: #f7fafc;
    border-radius: 8px;
    margin-bottom: 8px;
    align-items: center;
}

.record-item:nth-child(even) {
    background: #edf2f7;
}

.bet-content {
    font-family: monospace;
    background: #e2e8f0;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 0.9em;
}

.status-pending {
    color: #ed8936;
    font-weight: bold;
}

.status-win {
    color: #48bb78;
    font-weight: bold;
}

.status-lose {
    color: #f56565;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .number-selection {
        grid-template-columns: 1fr;
    }
    
    .user-stats {
        grid-template-columns: 1fr;
    }
    
    .records-header,
    .record-item {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .bet-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .bet-amount {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .user-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .recharge {
        flex-direction: column;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 0.6s ease-in-out;
}

/* 下注区域 */
.betting-area {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.next-issue-info {
    background: #e3f2fd;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
    text-align: center;
    border-left: 4px solid #2196f3;
}

.next-issue-info strong {
    color: #1976d2;
    font-size: 1.1em;
}

/* 提示消息 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}

.message.success {
    background: #48bb78;
}

.message.error {
    background: #f56565;
}

.message.info {
    background: #4299e1;
}

.message.warning {
    background: #ed8936;
}